2025-08-03 12:08:28,437 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:14,674 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:23,686 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,037 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:09:24,556 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,600 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:09:26,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:27,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:28,005 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:09:32,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:09:32,840 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:09:35,674 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:09:37,137 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c "HTTP/1.1 200 OK"
2025-08-03 12:09:40,277 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/navigate "HTTP/1.1 200 OK"
2025-08-03 12:09:53,749 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/act "HTTP/1.1 200 OK"
2025-08-03 12:09:59,371 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:03,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:03,479 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:07,094 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:12,220 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:17,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:17,471 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:17,547 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:22,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:22,335 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:25,914 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:30,164 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:37,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:37,991 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:39,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:40,572 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:48,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:48,509 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:49,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:49,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:50,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:51,638 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/end "HTTP/1.1 200 OK"
2025-08-03 12:11:52,363 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:12:37,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:37,608 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:12:38,060 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:38,102 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:12:38,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,601 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:12:44,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:12:44,265 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:12:47,223 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:12:48,687 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b "HTTP/1.1 200 OK"
2025-08-03 12:12:53,744 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/navigate "HTTP/1.1 200 OK"
2025-08-03 12:13:13,838 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/act "HTTP/1.1 200 OK"
2025-08-03 12:13:23,053 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:13:26,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:13:26,869 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:13:35,414 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/extract "HTTP/1.1 200 OK"
2025-08-03 12:14:06,461 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:21,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:21,552 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:21,609 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:55,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:56,004 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:56,122 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:09,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:09,932 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:09,973 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:26,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:26,287 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:26,302 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:43,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:43,104 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:43,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:44,771 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:53,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:53,658 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:54,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:55,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:56,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:58,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:16:00,879 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/end "HTTP/1.1 200 OK"
2025-08-03 12:24:00,902 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:05,618 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:24:15,003 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:15,895 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,248 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:24:16,739 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,784 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:24:18,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,543 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:23,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:23,963 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:26,829 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:24:28,287 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc "HTTP/1.1 200 OK"
2025-08-03 12:24:31,394 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/navigate "HTTP/1.1 200 OK"
2025-08-03 12:24:37,064 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:40,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:40,073 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:42,087 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information...
2025-08-03 12:24:43,885 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:24:49,238 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:24:49,265 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:53,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:53,020 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:55,032 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Identify and analyze elements on the page to ensur...
2025-08-03 12:24:56,495 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:10,792 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:10,834 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:14,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:14,920 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:16,933 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product title, description, prices, and ot...
2025-08-03 12:25:18,434 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:25:21,879 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:25:21,907 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:27,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:27,474 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:28,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,694 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:33,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:33,472 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:40,485 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Check if the page is accessible and properly loade...
2025-08-03 12:25:42,029 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:46,527 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:46,562 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:50,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:50,638 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:52,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:53,303 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:58,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:58,764 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:59,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,098 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:05,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:05,277 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:05,295 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:07,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:07,923 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:08,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:08,555 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:12,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:12,219 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:12,329 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:26:13,668 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/end "HTTP/1.1 200 OK"
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:26:33,399 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:38,523 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:26:48,450 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:49,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:49,955 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:26:50,486 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:50,545 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:26:51,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,972 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:58,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:58,356 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:01,261 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:27:02,700 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0 "HTTP/1.1 200 OK"
2025-08-03 12:27:06,401 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:27:12,413 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:17,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:17,301 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:24,314 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:27:26,143 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/extract "HTTP/1.1 200 OK"
2025-08-03 12:27:37,287 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:27:37,308 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:47,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:47,364 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:47,411 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:55,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:55,619 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:56,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,902 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:02,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:02,377 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:03,653 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:28:08,482 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:24,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:24,426 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:25,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:27,080 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:37,168 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:37,176 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:38,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:40,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:42,127 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:46,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:46,414 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:53,440 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Inspect the page source for structured data or hid...
2025-08-03 12:28:55,298 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/observe "HTTP/1.1 200 OK"
2025-08-03 12:28:57,288 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:28:57,323 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:10,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:10,730 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:10,754 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:23,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:23,703 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:24,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:24,514 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:34,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:34,067 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:34,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:34,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,983 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:29:38,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/end "HTTP/1.1 200 OK"
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:30:24,998 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:30,935 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:30:40,410 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:41,356 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:41,774 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:30:42,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:42,347 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:30:43,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,513 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:30:49,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:30:49,559 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:30:52,423 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:30:53,963 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1 "HTTP/1.1 200 OK"
2025-08-03 12:31:00,808 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/navigate "HTTP/1.1 200 OK"
2025-08-03 12:31:06,857 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:10,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:10,648 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:12,659 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all product information including title, d...
2025-08-03 12:31:15,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/extract "HTTP/1.1 200 OK"
2025-08-03 12:31:21,709 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:31:21,730 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:27,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:27,931 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:27,984 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:39,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:39,165 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:40,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:41,320 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:47,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:47,411 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:48,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:50,003 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:31:51,410 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/end "HTTP/1.1 200 OK"
2025-08-03 12:31:51,583 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:31:51,584 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:33:45,320 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:33:50,874 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:33:59,607 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:00,467 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:00,854 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:01,451 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:01,492 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:01,500 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:02,546 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:18,632 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:23,962 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:34:33,254 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:34,155 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:34,536 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:35,049 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:35,093 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:35,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,309 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:41,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:41,390 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:34:44,186 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:34:45,730 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d "HTTP/1.1 200 OK"
2025-08-03 12:34:48,875 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/navigate "HTTP/1.1 200 OK"
2025-08-03 12:34:55,117 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:58,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:58,961 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:00,973 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:35:03,579 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/extract "HTTP/1.1 200 OK"
2025-08-03 12:35:08,931 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:35:08,959 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:13,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:13,430 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:13,495 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:20,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:20,368 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:21,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:22,035 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:30,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:30,930 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:31,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:31,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,250 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:35:34,621 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/end "HTTP/1.1 200 OK"
2025-08-03 12:35:34,706 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:35:34,707 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:40:34,397 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:40:39,454 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:40:48,666 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:40:58,827 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:41:04,279 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:41:13,963 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:41:14,894 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:41:15,289 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:41:15,786 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:41:15,839 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:41:17,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:41:18,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:41:18,312 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:23,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:23,163 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:25,922 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:41:27,451 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50 "HTTP/1.1 200 OK"
2025-08-03 12:41:30,721 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/navigate "HTTP/1.1 200 OK"
2025-08-03 12:41:37,019 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:40,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:40,884 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:42,893 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:41:44,670 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/extract "HTTP/1.1 200 OK"
2025-08-03 12:41:54,553 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:41:54,574 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:58,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:58,765 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:58,816 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:42:05,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:42:05,299 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:42:06,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:07,236 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:42:15,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:42:15,354 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:42:15,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:19,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:19,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:20,019 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:42:21,395 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/end "HTTP/1.1 200 OK"
2025-08-03 12:42:21,477 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:42:21,478 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 13:48:07,550 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:48:12,021 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 13:48:19,748 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:49:07,431 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:49:11,992 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:05:33,583 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:05:38,728 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:05:48,572 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_140548_c237aabd
2025-08-03 14:05:48,577 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_140548_c237aabd - test_vendor/test_category
2025-08-03 14:12:02,921 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:12:07,551 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:12:15,581 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141215_b50fc341
2025-08-03 14:12:15,583 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_141215_b50fc341 - test_vendor/test_category
2025-08-03 14:12:51,110 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:12:55,480 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:13:58,155 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:14:03,300 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:16:57,966 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:17:02,710 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:17:10,715 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_efb5b7db
2025-08-03 14:17:10,726 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_34258faf
2025-08-03 14:17:10,730 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_ec048420
2025-08-03 14:20:00,724 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:20:06,150 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:20:15,603 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_9fb59a47
2025-08-03 14:20:15,604 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_89821708
2025-08-03 14:20:15,604 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_test_category_1754227215 - test_vendor/test_category
2025-08-03 14:20:15,613 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_c75acef1
2025-08-03 14:20:15,618 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_cfe6989e
2025-08-03 14:20:15,618 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_8abeff79
2025-08-03 14:20:15,619 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_0_1754227215 - test_vendor/category_0
2025-08-03 14:20:15,619 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_db0af18b
2025-08-03 14:20:15,620 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_1_1754227215 - test_vendor/category_1
2025-08-03 14:20:15,620 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_95bf2052
2025-08-03 14:20:15,621 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_2_1754227215 - test_vendor/category_2
2025-08-03 14:23:07,633 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:23:13,800 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:23:25,547 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_c32e9179
2025-08-03 14:23:25,566 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_b0cc8488
2025-08-03 14:23:25,566 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_test_category_1754227405 - test_vendor/test_category
2025-08-03 14:23:25,567 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_694661a2
2025-08-03 14:23:25,568 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_2_test_category_2_1754227405 - test_vendor_2/test_category_2
2025-08-03 14:23:25,598 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_13ee0db2
2025-08-03 14:23:25,600 - ecommerce_scraper.state.state_manager - INFO - Resumed session: scraping_20250803_142325_13ee0db2
2025-08-03 14:23:25,667 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_0901903a
2025-08-03 14:23:25,669 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_8de9e256
2025-08-03 14:23:25,669 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_0_1754227405 - test_vendor/category_0
2025-08-03 14:23:25,671 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_9008135a
2025-08-03 14:23:25,672 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_1_1754227405 - test_vendor/category_1
2025-08-03 14:23:25,673 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_8b2dfa93
2025-08-03 14:23:25,674 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_2_1754227405 - test_vendor/category_2
2025-08-03 14:36:11,369 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:36:17,263 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:37:29,556 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:37:35,486 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:38:11,349 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:38:16,591 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:39:46,200 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:39:51,550 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:40:53,472 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:40:58,830 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:41:08,546 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144108_280f663b
2025-08-03 14:41:34,459 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:41:40,507 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:41:50,137 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144150_83fb18ff
2025-08-03 14:43:05,208 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:43:11,393 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:43:21,913 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144321_057d752d
2025-08-03 15:03:32,740 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:03:37,676 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:04:28,700 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:04:32,713 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:14:16,193 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:14:20,471 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:14:39,868 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:14:44,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:14:44,803 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:14:47,367 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 15:14:48,715 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452 "HTTP/1.1 200 OK"
2025-08-03 15:14:52,086 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/navigate "HTTP/1.1 200 OK"
2025-08-03 15:15:05,929 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:15:10,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:15:10,085 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:15:15,089 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Analyze the main navigation structure for product ...
2025-08-03 15:15:16,912 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/observe "HTTP/1.1 200 OK"
2025-08-03 15:15:21,025 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 15:15:21,046 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:15:25,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:15:25,476 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:15:29,480 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract category names, URLs, and product counts f...
2025-08-03 15:15:31,090 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/extract "HTTP/1.1 200 OK"
2025-08-03 15:15:31,902 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: cannot reuse already awaited coroutine
2025-08-03 15:15:32,903 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract category names, URLs, and product counts f...
2025-08-03 15:19:56,276 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/end "HTTP/1.1 410 Gone"
2025-08-03 15:20:30,916 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:20:36,117 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:20:59,642 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:04,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:04,504 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:04,511 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 15:21:04,527 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:08,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:08,089 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:08,106 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:11,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:11,618 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:11,621 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 15:21:11,653 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:26,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:26,660 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:26,662 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:37,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:37,542 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:48:39,669 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:49:11,624 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:50:05,179 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:56:24,051 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:57:43,643 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:57:54,790 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:00:16,907 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:00:22,033 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:05:16,026 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:05:22,441 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:22:23,035 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:22:28,359 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:27:02,780 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:27:09,991 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:46:02,175 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:46:06,872 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:48:51,563 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:48:55,991 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:56:20,377 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:56:26,322 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:57:02,008 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:57:06,977 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:57:31,804 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:57:36,648 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 18:57:46,055 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 18:57:53,795 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:00:45,369 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:00:50,207 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:01:23,703 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:01:28,015 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:02:13,659 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:02:18,319 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:15:05,754 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:15:11,340 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:16:38,581 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:16:43,275 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:19:38,666 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:19:45,046 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:21:39,751 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:31,407 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:37,409 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:22:54,523 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:59,578 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:35:38,470 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:35:42,821 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:36:07,680 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:36:12,218 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:36:29,199 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:36:33,916 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:37:38,590 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:37:43,075 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:37:50,972 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246270 for asda/Rollback
2025-08-03 19:37:50,973 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754246270 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:38:21,093 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:38:25,460 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:38:34,616 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246314 for asda/Rollback
2025-08-03 19:38:34,616 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754246314 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:40:17,588 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:40:22,110 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:40:30,267 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246430 for asda/Rollback
2025-08-03 19:40:30,268 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754246430 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:41:44,837 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:41:49,262 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:41:57,177 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246517 for asda/Rollback
2025-08-03 19:52:15,268 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:52:19,839 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:53:42,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247222 for asda/Rollback
2025-08-03 19:53:42,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754247222 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:53:42,152 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 19:53:42,152 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247222
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754247222
2025-08-03 19:53:42,156 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,156 - ecommerce_scraper.state.state_manager - ERROR - Failed to save state: [Errno 22] Invalid argument: 'scraping_state\\scraping_20250803_195303_asda_Rollback > Laundry, Household & Toiletries.json'
2025-08-03 19:53:42,159 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,161 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195303 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:53:42,163 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 19:53:42,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,185 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,185 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 0 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,186 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247222 (attempt 1)
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 1 error scraping URL https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 processing job: asda_Rollback_1754247222
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,190 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754247222 (attempt 1)
2025-08-03 19:53:42,192 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 19:53:42,192 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,198 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,198 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 2 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,199 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,199 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247222 (attempt 2)
2025-08-03 19:53:42,201 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 19:53:42,202 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 19:53:42,202 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 19:58:33,615 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:58:40,220 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:58:51,675 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:58:51,675 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247531 for asda/Rollback
2025-08-03 19:59:36,692 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:59:41,696 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:59:50,658 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:59:50,670 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247590 for asda/Rollback
2025-08-03 19:59:50,673 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247590
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 19:59:50,676 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195950 - asda/Rollback
2025-08-03 19:59:50,678 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:59:50,688 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:59:50,720 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 19:59:50,726 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:59:53,360 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 19:59:54,790 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 19:59:55,366 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 19:59:55,915 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 19:59:57,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 19:59:57,287 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 19:59:59,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 19:59:59,056 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 19:59:59,091 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:03,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:03,657 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:03,672 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:03,688 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:07,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:07,752 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:07,768 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:19,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:19,132 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:20,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:20,714 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:00:21,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:21,384 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:00:21,393 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:25,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:25,347 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:25,351 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:25,365 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:28,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:28,981 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:29,000 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:33,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:34,184 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:34,187 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:34,210 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:39,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:39,294 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:39,296 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:49,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:49,326 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:50,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:50,626 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:00:52,314 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:55,130 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,130 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,147 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247655 for asda/Rollback
2025-08-03 20:00:55,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:00:55,154 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:00:55,155 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247655
2025-08-03 20:00:55,155 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:00:55,157 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,158 - ecommerce_scraper.state.state_manager - ERROR - Failed to save state: [Errno 22] Invalid argument: 'scraping_state\\scraping_20250803_200053_asda_Rollback > Laundry, Household & Toiletries.json'
2025-08-03 20:00:55,161 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,162 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200053 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:00:55,167 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:00:55,170 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,172 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,173 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,174 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,196 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,196 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,218 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,219 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:00:55,221 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 0 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,223 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 1 error scraping URL https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,223 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,224 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,225 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247655 (attempt 1)
2025-08-03 20:00:55,225 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 (attempt 1)
2025-08-03 20:00:55,228 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 processing job: asda_Rollback_1754247655
2025-08-03 20:00:55,229 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:00:55,230 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,230 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:00:55,232 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,233 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,236 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,249 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,249 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 2 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,250 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,250 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247655 (attempt 2)
2025-08-03 20:00:55,251 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:00:55,253 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:00:55,254 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:05,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:05,949 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:06,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:06,816 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:07,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:08,061 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:08,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:08,581 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:09,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:10,898 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:11,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:13,178 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:14,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:14,401 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:14,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:15,385 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:15,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:15,846 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:16,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:16,351 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:17,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:18,177 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:18,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:18,799 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:18,824 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:24,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:24,640 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:24,644 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:24,662 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:25,812 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:01:25,813 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:01:30,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:30,868 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:31,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:31,928 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:01:33,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:33,782 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:33,791 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:37,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:37,754 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:37,758 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:37,776 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:42,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:42,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:42,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:45,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:46,094 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:46,098 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:46,120 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:49,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:49,358 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:49,362 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:49,376 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:52,853 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:52,863 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:59,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:59,562 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:03:21,635 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:03:26,160 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:03:57,814 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,814 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,815 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,826 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247837 for asda/Rollback
2025-08-03 20:03:57,827 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754247837 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,831 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247837
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:03:57,833 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:03:57,833 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754247837
2025-08-03 20:03:57,837 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,839 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:03:57,840 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,841 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:03:57,842 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,844 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,845 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,847 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,860 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,861 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,876 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,876 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,892 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:03:57,892 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:03:58,797 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:03:59,094 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:03:59,326 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:03:59,677 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:03:59,727 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:03:59,960 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:04:00,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:00,673 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:00,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:00,938 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:01,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:01,349 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:01,374 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:02,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:02,535 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:02,542 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:06,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:06,365 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:06,372 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:06,383 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:06,585 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:06,590 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:06,595 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:06,605 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:09,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:09,676 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:09,680 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:09,689 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:11,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:11,486 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:12,190 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:12,194 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:13,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:13,868 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:13,874 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:16,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:16,568 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:17,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:17,827 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:18,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:18,200 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:18,210 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:18,219 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:19,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:19,317 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:19,322 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:21,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:21,619 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:21,622 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:21,632 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:21,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:21,833 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:21,837 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:21,849 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:22,858 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:04:24,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:24,290 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:24,294 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:24,305 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:24,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:24,966 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:24,972 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:24,984 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:27,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:27,015 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:27,018 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:27,029 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:28,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:28,242 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:28,255 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:29,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:29,682 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:29,686 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:29,698 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:31,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:31,362 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:31,365 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:35,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:35,242 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:35,244 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:35,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:35,844 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:36,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:36,950 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:37,619 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:38,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:38,188 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:39,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:39,672 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:39,677 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:42,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:42,660 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:42,694 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:43,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:43,418 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:44,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,013 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:44,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,426 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:44,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,790 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:45,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:45,452 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:45,463 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:48,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:48,910 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:49,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:49,901 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:50,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:50,076 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:50,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:50,641 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:50,648 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:50,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:50,758 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:50,764 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:53,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:53,233 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:53,262 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:04:53,262 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraped 0 products from https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:04:53,267 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754247837 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:04:53,269 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754247837 (attempt 1)
2025-08-03 20:04:53,270 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:04:57,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:57,285 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:57,290 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:57,301 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:05:10,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:05:10,417 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:05:10,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:05:10,979 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:05:10,985 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:05:18,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:05:18,988 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:05:18,996 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:07:40,815 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:07:45,868 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:08:00,749 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:08:00,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:08:00,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:08:00,751 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:08:00,752 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:08:00,761 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754248080 for asda/Rollback
2025-08-03 20:08:00,765 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:08:00,765 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754248080
2025-08-03 20:08:00,766 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:08:00,766 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:08:00,767 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:08:00,768 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:08:00,770 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:08:00,774 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:08:00,775 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:08:00,775 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:08:00,776 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:08:00,776 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:08:00,793 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:08:00,808 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:08:01,710 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:08:02,080 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:08:02,641 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:08:02,690 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:08:05,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:05,974 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:13,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:13,574 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:08:13,599 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:19,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:19,120 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:19,126 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:19,143 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:24,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:24,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:24,320 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:24,337 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:29,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:29,863 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:31,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:31,533 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:33,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:33,204 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:08:33,211 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:38,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:38,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:38,043 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:38,057 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:41,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:41,064 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:41,068 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:41,084 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:44,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:44,712 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:44,715 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:44,736 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:47,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:47,380 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:47,400 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:50,903 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:50,905 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:50,908 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:53,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:53,756 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:55,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:55,190 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:55,902 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:59,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:59,295 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:59,330 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:03,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:03,497 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:03,515 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:07,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:07,738 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:08,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:09:08,646 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:09:10,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:09:10,402 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:09:10,410 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:15,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:30,919 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:09:30,919 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:09:57,374 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:57,378 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:09:57,392 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:58,996 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:09:58,997 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:10:24,293 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:10:29,507 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:12:48,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,239 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,240 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,240 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,241 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,255 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754248368 for asda/Rollback
2025-08-03 20:12:48,256 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754248368 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754248368
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754248368
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:12:48,263 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,265 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:12:48,268 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,268 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:12:48,269 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,271 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,272 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,272 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,275 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,275 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,276 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,276 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,278 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,290 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,290 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,303 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,303 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,317 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:12:48,321 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:12:49,236 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:12:49,639 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:12:49,764 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:12:50,214 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:12:50,255 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:12:50,374 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:12:51,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:52,004 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:12:52,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:52,674 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:12:59,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:59,228 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:12:59,261 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:03,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:03,672 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:03,683 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:03,697 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:04,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:04,072 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:04,081 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:07,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:07,755 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:07,760 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:07,768 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:08,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:08,738 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:08,743 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:08,753 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:12,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:12,783 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:12,786 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:12,795 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:13,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:13,494 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:15,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:15,031 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:17,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:17,377 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:17,388 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:18,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:18,481 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:20,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:20,571 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:20,579 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:20,602 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:23,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:23,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:23,790 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:23,802 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:23,825 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:24,387 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:25,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:26,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:26,011 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:26,014 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:26,025 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:28,090 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:28,096 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:28,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:28,795 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:28,798 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:28,809 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:32,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:32,669 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:32,673 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:32,684 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:32,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:32,960 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:32,962 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:35,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:35,255 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:35,258 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:35,269 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:35,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:35,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:36,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:36,784 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:37,446 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:38,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:38,663 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:38,666 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:38,679 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:41,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:41,092 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:41,094 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:42,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:42,865 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:43,359 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:13:43,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:43,580 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:43,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:43,823 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:43,830 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:44,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:44,468 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:45,034 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:45,614 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,635 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:48,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:48,867 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:49,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:49,928 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:50,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:50,471 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:50,493 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:52,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:52,742 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:53,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:53,598 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:54,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:54,296 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:54,305 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:55,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:55,158 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:55,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:55,869 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:56,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:56,665 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:56,674 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:59,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:59,499 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:59,504 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:59,521 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:01,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:01,313 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:01,316 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:01,333 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:05,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:05,165 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:05,172 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:05,189 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:09,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:09,761 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:09,764 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:09,788 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:13,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:13,456 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:13,459 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:13,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:13,996 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:14,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:14,659 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:14,663 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:19,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:19,816 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:20,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:20,556 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:20,561 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:20,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:20,677 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:21,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:21,948 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:22,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:22,684 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:23,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:23,171 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:23,185 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:27,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:27,231 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:27,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:27,863 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:27,867 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:27,883 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:28,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,036 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:28,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,345 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:28,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,665 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:29,056 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:29,376 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,388 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:32,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:32,429 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:32,432 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:33,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:33,237 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:34,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:34,534 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:34,538 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:38,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:38,201 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:38,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:39,000 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:39,033 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:14:39,033 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraped 0 products from https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:14:39,034 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754248368 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:14:39,035 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754248368 (attempt 1)
2025-08-03 20:14:39,036 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:14:39,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:39,083 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:39,088 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:28:40,780 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:28:45,224 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:28:53,171 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:28:53,172 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:28:53,173 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:28:53,174 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:28:53,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,176 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:28:53,177 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:28:53,178 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,191 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754249333 for asda/Rollback
2025-08-03 20:28:53,194 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor started in main thread
2025-08-03 20:28:53,194 - ecommerce_scraper.batch.batch_processor - INFO - Started Stagehand processor thread
2025-08-03 20:28:53,195 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754249333
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:28:53,197 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:28:53,199 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:28:53,209 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:28:53,210 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_-1521094302663175139_0 for worker 0
2025-08-03 20:28:53,211 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:28:53,212 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:28:53,213 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:28:53,214 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:28:53,215 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,216 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:28:53,216 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:28:53,217 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,229 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:28:54,025 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:28:54,320 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:28:54,776 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:28:54,811 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:28:55,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:28:55,679 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:28:56,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:28:56,434 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:28:56,457 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:02,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:02,543 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:02,549 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:02,563 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:09,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:09,085 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:09,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:09,746 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:29:11,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:11,247 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:29:11,254 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:16,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:16,352 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:16,355 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:16,369 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:21,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:21,620 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:21,634 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:26,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:26,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:26,042 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:26,063 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:39,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:39,073 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:39,075 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:44,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:44,450 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:56,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:56,093 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:29:56,807 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:02,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:02,304 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:03,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:03,556 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:04,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:04,134 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:04,148 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:09,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:09,636 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:09,656 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:14,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:14,527 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:15,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:15,200 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:15,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:15,985 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:15,992 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:22,003 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:22,013 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:22,016 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:30:22,034 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:34,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:34,849 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:35,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:35,701 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:35,706 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:43,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:43,209 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:43,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:43,836 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,187 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:44,196 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:44,528 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,544 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:47,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:47,379 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:48,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:48,168 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:48,173 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:52,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:52,695 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:52,726 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:30:52,726 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_-1521094302663175139_0, 0 products
2025-08-03 20:30:53,346 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,347 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249333 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:30:53,348 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249333 (attempt 1)
2025-08-03 20:30:53,350 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback_1754249333
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_-1521094302663175139_1 for worker 1
2025-08-03 20:30:53,353 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:30:53,354 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:30:53,354 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:30:53,355 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:30:53,357 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:30:53,358 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:30:53,359 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:30:53,360 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:30:53,363 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:30:53,992 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:30:54,537 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:30:55,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:55,291 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:55,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:56,067 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:56,083 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:01,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:01,294 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:01,298 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:31:01,314 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:05,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:05,973 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:05,989 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:13,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:13,772 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:14,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:14,605 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:31:15,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:15,428 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:15,435 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:18,400 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:31:18,469 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:31:24,043 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:31:28,559 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:31:34,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:34,105 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:35,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:35,103 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:31:35,108 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:45,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:45,919 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:46,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:46,711 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:47,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:47,384 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:48,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:48,773 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,118 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,439 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,951 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:50,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:50,741 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:51,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:51,091 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:51,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:51,658 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:53,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:53,183 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:53,480 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:00,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:00,187 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:01,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:01,761 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:01,767 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:05,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:05,371 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:05,403 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:32:05,403 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_-1521094302663175139_1, 0 products
2025-08-03 20:32:05,404 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor stopped
2025-08-03 20:32:05,404 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:32:05,405 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:32:05,510 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:05,511 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249333 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:32:05,512 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249333 (attempt 2)
2025-08-03 20:32:05,516 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:32:16,222 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:32:16,223 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:32:16,224 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:32:16,224 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:32:16,225 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,226 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:32:16,229 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:32:16,232 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,233 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:32:16,251 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754249536 for asda/Rollback
2025-08-03 20:32:16,252 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754249536 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,258 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor started in main thread
2025-08-03 20:32:16,258 - ecommerce_scraper.batch.batch_processor - INFO - Started Stagehand processor thread
2025-08-03 20:32:16,259 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754249536
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754249536
2025-08-03 20:32:16,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:32:16,261 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:32:16,263 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:32:16,266 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,271 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:16,273 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:32:16,274 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:16,274 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 delegating Stagehand operation to main thread for: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:32:16,275 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_8249459401766103418_0 for worker 0
2025-08-03 20:32:16,279 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:32:16,280 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:32:16,282 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:32:16,283 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:32:16,285 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,287 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:32:16,287 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:32:16,288 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,289 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:32:16,307 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:32:16,324 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:17,149 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:32:17,470 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:32:18,035 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:32:18,091 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:32:19,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:19,817 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:20,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:20,349 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:32:20,372 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:25,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:25,248 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:25,256 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:32:25,275 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:32,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:32,444 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:33,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:33,365 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:34,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:34,866 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:32:34,873 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:41,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:41,140 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:41,144 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:32:41,162 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:55,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:55,381 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:56,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:56,306 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:57,667 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:05,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:05,198 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:06,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:06,610 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:07,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:07,117 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:07,152 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:14,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:14,117 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:14,120 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:33:14,142 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:22,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:22,012 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:22,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:22,788 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:23,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:23,483 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:23,493 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:27,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:27,813 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:27,832 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:30,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:30,848 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:30,864 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:38,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:38,535 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:39,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:39,689 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:39,693 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:47,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:47,739 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:48,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:48,568 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:49,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:49,107 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:50,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:50,021 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:50,038 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:54,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:54,265 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:56,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:56,137 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:56,142 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:34:01,410 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:34:03,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:34:03,726 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:34:03,751 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:34:03,751 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_8249459401766103418_0, 0 products
2025-08-03 20:34:03,752 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor stopped
2025-08-03 20:34:04,340 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:34:04,340 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249536 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:34:04,341 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249536 (attempt 1)
2025-08-03 20:34:04,343 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:34:34,344 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:34:34,344 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
