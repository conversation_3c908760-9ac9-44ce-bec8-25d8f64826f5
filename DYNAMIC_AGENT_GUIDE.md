# Dynamic Multi-Agent Scraping Guide

## 🎯 Overview

This system replaces the problematic BatchProcessor threading approach with CrewAI's native multi-agent orchestration to eliminate the "signal only works in main thread" errors while maintaining concurrent scraping capabilities.

## 🤖 How Dynamic Agent Delegation Works

### The Complete Flow

```
1. User Selection
   ├── Vendors: ASDA, Tesco, etc.
   ├── Categories: Electronics, Clothing, etc.
   └── Scope: Recent (3 pages) vs Complete (all pages)
   
2. Plan Creation
   ├── Session ID: scraping_20250803_203045
   ├── Scope Configuration: max_pages = 3 or None
   ├── Estimated Products & Duration
   └── URL Extraction from Categories
   
3. Dynamic Agent Orchestration
   ├── CategoryDiscoveryAgent
   │   ├── Navigates: "Electronics" main page
   │   ├── Discovers: ["Phones", "Laptops", "Tablets"]
   │   └── Returns: Subcategory URLs + metadata
   │
   ├── SubCategoryScraperAgent_1 (Phones)
   │   ├── Own Stagehand Tool (Browserbase session #1)
   │   ├── Scrapes: Up to 3 pages (if scope = "recent")
   │   └── Returns: Phone products
   │
   ├── SubCategoryScraperAgent_2 (Laptops)
   │   ├── Own Stagehand Tool (Browserbase session #2)
   │   ├── Scrapes: Up to 3 pages (if scope = "recent")
   │   └── Returns: Laptop products
   │
   └── SubCategoryScraperAgent_3 (Tablets)
       ├── Own Stagehand Tool (Browserbase session #3)
       ├── Scrapes: Up to 3 pages (if scope = "recent")
       └── Returns: Tablet products
   
4. CrewAI Orchestration
   ├── Runs agents concurrently (no threading issues!)
   ├── Each agent operates independently
   ├── Automatic error isolation
   └── Results combined into final dataset
```

## 🔧 Key Components

### 1. Scope Configuration

```python
SCRAPING_SCOPES = {
    "recent": {
        "name": "Recent products only",
        "description": "First 2-3 pages per category (faster, good for testing)",
        "max_pages": 3
    },
    "complete": {
        "name": "All available products", 
        "description": "Complete product catalog (comprehensive, slower)",
        "max_pages": None
    }
}
```

**How it works:**
- User selects scope during interactive setup
- `max_pages` parameter passed to each SubCategoryScraperAgent
- Agents respect pagination limits based on scope

### 2. Plan Integration

```python
plan = {
    "session_id": "scraping_20250803_203045",
    "scope": "recent",
    "vendors": {...},
    "total_estimated_products": 500,
    "estimated_duration_minutes": 30
}
```

**How it works:**
- Plan created during interactive setup
- Session ID used for tracking and logging
- Scope configuration passed to dynamic scraper
- Progress tracked against plan estimates

### 3. Dynamic Agent Creation

```python
# For each discovered subcategory
agent = SubCategoryScraperAgent.create_agent("Phones", agent_id=1)
task = SubCategoryScraperAgent.create_scraping_task(
    subcategory_data={"name": "Phones", "url": "..."},
    vendor="asda",
    agent=agent,
    max_pages=3  # From scope configuration
)
```

**How it works:**
- Agents created on-demand based on discovered subcategories
- Each agent gets specialized role and tools
- Max pages configuration from scope passed to each agent
- Independent Browserbase sessions prevent conflicts

## 🚀 Advantages Over BatchProcessor

| Aspect | BatchProcessor (Old) | Dynamic Agents (New) |
|--------|---------------------|---------------------|
| **Threading** | Custom worker threads → Signal errors | CrewAI native execution ✅ |
| **Concurrency** | Fixed thread pool | Dynamic agent creation ✅ |
| **Error Handling** | Batch-level failures | Agent-level isolation ✅ |
| **Scope Integration** | ❌ Not implemented | ✅ Fully integrated |
| **Plan Tracking** | ❌ Limited | ✅ Session ID + progress |
| **Observability** | Basic batch progress | Per-agent detailed logging ✅ |
| **Scalability** | Fixed worker count | Scales with subcategories ✅ |

## 📊 Scope Impact Examples

### Recent Scope (max_pages: 3)
```
Electronics Category
├── Phones: Pages 1-3 → ~75 products
├── Laptops: Pages 1-3 → ~75 products  
└── Tablets: Pages 1-3 → ~75 products
Total: ~225 products in ~6 minutes
```

### Complete Scope (max_pages: None)
```
Electronics Category  
├── Phones: All pages → ~500 products
├── Laptops: All pages → ~400 products
└── Tablets: All pages → ~300 products
Total: ~1200 products in ~30 minutes
```

## 🔍 Session Tracking

Each scraping session gets:
- **Unique Session ID**: `scraping_20250803_203045`
- **Plan File**: `scraping_plans/scraping_20250803_203045.json`
- **Agent Logs**: Individual agent progress and results
- **Progress Tracking**: Real-time updates per agent

## 🛠️ Configuration

### Dynamic Scraper Initialization
```python
dynamic_scraper = DynamicMultiAgentScraper(
    max_concurrent_agents=3,           # Max agents per category
    max_pages_per_category=3,          # From scope selection
    session_id="scraping_20250803_203045"  # From plan
)
```

### Agent Task Configuration
```python
task_description = f"""
Scrape products from: {subcategory['name']}
Max pages to scrape: {max_pages or 'All available'}

Your task is to:
1. Navigate to the subcategory page
2. Extract all visible products with complete details
3. Handle pagination to get products from up to {max_pages} pages
4. Validate and standardize the product data
...
"""
```

## 🎯 Next Steps

1. **Test the Integration**: Run `python enhanced_interactive_scraper.py`
2. **Monitor Agent Behavior**: Check that scope limits are respected
3. **Verify Session Tracking**: Ensure plan files are created correctly
4. **Performance Testing**: Compare recent vs complete scope timing

The dynamic agent approach maintains all the planning and scoping functionality while eliminating threading issues through CrewAI's native orchestration capabilities.
