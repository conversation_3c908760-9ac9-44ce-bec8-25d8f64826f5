#!/usr/bin/env python3
"""Dynamic multi-agent scraper using CrewAI's native orchestration."""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool

from ..tools.stagehand_tool import EcommerceStagehandTool
from ..tools.product_data_validator import ProductDataValidator
from ..tools.price_extractor import PriceExtractor
from ..tools.image_extractor import ImageExtractor
from ..config.settings import settings
from ..models.product import StandardizedProduct

logger = logging.getLogger(__name__)


class DynamicScrapingResult:
    """Result from dynamic scraping operation."""
    
    def __init__(self, success: bool, products: List[StandardizedProduct] = None, 
                 error: str = None, agent_results: List[Dict] = None):
        self.success = success
        self.products = products or []
        self.error = error
        self.agent_results = agent_results or []
        self.timestamp = datetime.now()
        self.total_products = len(self.products)



class CategoryScraperAgent:
    """Agent responsible for scraping products from a specific category."""

    @staticmethod
    def create_product_scraper_agent(category_name: str, agent_id: int) -> Agent:
        """Create a product scraper agent."""
        return Agent(
            role=f"Product Scraper #{agent_id}",
            goal=f"Extract all product data from the {category_name} category",
            backstory=f"""You are a specialized product scraper focused on the
            {category_name} category. You excel at navigating product listings,
            extracting detailed product information, and handling pagination to
            ensure complete data collection.""",
            tools=[EcommerceStagehandTool.create_with_context()],
            verbose=True,
            allow_delegation=False
        )

    @staticmethod
    def create_data_extractor_agent(category_name: str, agent_id: int) -> Agent:
        """Create a data extraction agent."""
        return Agent(
            role=f"Data Extractor #{agent_id}",
            goal=f"Extract and standardize product data from {category_name}",
            backstory=f"""You are an expert at extracting structured data from
            ecommerce pages. You specialize in identifying product information,
            prices, images, and metadata from {category_name} listings.""",
            tools=[
                EcommerceStagehandTool.create_with_context(),
                PriceExtractor(),
                ImageExtractor()
            ],
            verbose=True,
            allow_delegation=False
        )

    @staticmethod
    def create_data_validator_agent(category_name: str, agent_id: int) -> Agent:
        """Create a data validation agent."""
        return Agent(
            role=f"Data Validator #{agent_id}",
            goal=f"Validate and standardize extracted product data from {category_name}",
            backstory=f"""You are a quality assurance specialist focused on
            ensuring data accuracy and completeness. You validate product information
            extracted from {category_name} and ensure it meets standardization requirements.""",
            tools=[ProductDataValidator()],
            verbose=True,
            allow_delegation=False
        )
    
    @staticmethod
    def create_product_scraping_task(category_data: Dict, vendor: str, agent: Agent, max_pages: Optional[int] = None) -> Task:
        """Create a task for scraping a specific subcategory."""
        pagination_instruction = f"Handle pagination to get products from up to {max_pages} pages" if max_pages else "Handle pagination to get all products"

        return Task(
            description=f"""
            Scrape products from the category: {category_data['name']}
            URL: {category_data['url']}
            Max pages to scrape: {max_pages or 'All available'}

            Your task is to:
            1. Navigate to the category page
            2. Extract all visible products with complete details
            3. {pagination_instruction}
            4. Validate and standardize the product data
            5. Extract high-quality product images
            6. Ensure price information is accurate
            
            For each product, extract:
            - Name/title
            - Price (current and original if on sale)
            - Description
            - Images (main and additional)
            - Availability status
            - Product variants (size, color, etc.)
            - SKU/product ID if available
            - Ratings/reviews if present
            
            Vendor: {vendor}
            Category: {category_data['name']}
            """,
            expected_output="""A JSON list of standardized products:
            [
                {
                    "name": "product_name",
                    "price": "current_price",
                    "original_price": "original_price_if_different",
                    "description": "product_description",
                    "images": ["image_url_1", "image_url_2"],
                    "availability": "in_stock|out_of_stock|limited",
                    "variants": [{"type": "size", "value": "M"}],
                    "sku": "product_sku",
                    "rating": "4.5",
                    "review_count": "123"
                }
            ]""",
            agent=agent
        )

    @staticmethod
    def create_data_extraction_task(category_data: Dict, vendor: str, agent: Agent) -> Task:
        """Create a task for extracting and standardizing product data."""
        return Task(
            description=f"""
            Extract and standardize product data from the category: {category_data['name']}

            Your task is to:
            1. Take the raw product data from the Product Scraper
            2. Extract and validate price information using PriceExtractor
            3. Extract and validate image URLs using ImageExtractor
            4. Standardize the data format
            5. Ensure data quality and completeness

            Focus on:
            - Price extraction and validation
            - Image URL extraction and validation
            - Data standardization and formatting
            - Quality assurance checks

            Vendor: {vendor}
            Category: {category_data['name']}
            """,
            expected_output="""Extracted and standardized product data in JSON format with validated prices and images.""",
            agent=agent
        )

    @staticmethod
    def create_data_validation_task(category_data: Dict, vendor: str, agent: Agent) -> Task:
        """Create a task for validating extracted product data."""
        return Task(
            description=f"""
            Validate and finalize product data from the category: {category_data['name']}

            Your task is to:
            1. Take the extracted product data from the Data Extractor
            2. Validate data completeness and accuracy using ProductDataValidator
            3. Ensure all required fields are present
            4. Standardize the final output format
            5. Generate the final StandardizedProduct objects

            Validation checks:
            - Required fields presence
            - Data format consistency
            - Price validation
            - URL validation
            - Image URL validation

            Vendor: {vendor}
            Category: {category_data['name']}
            """,
            expected_output="""Final validated StandardizedProduct objects in JSON format ready for storage.""",
            agent=agent
        )


class DynamicMultiAgentScraper:
    """Main orchestrator for dynamic multi-agent scraping."""

    def __init__(self, max_concurrent_agents: int = 3, max_pages_per_category: Optional[int] = None, session_id: str = None):
        """Initialize the dynamic scraper.

        Args:
            max_concurrent_agents: Maximum number of agents to run concurrently
            max_pages_per_category: Maximum pages to scrape per category (None = all pages)
            session_id: Session ID for tracking and logging
        """
        self.max_concurrent_agents = max_concurrent_agents
        self.max_pages_per_category = max_pages_per_category
        self.session_id = session_id or f"dynamic_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger = logging.getLogger(__name__)

    def scrape_category_directly(
        self,
        category_url: str,
        vendor: str,
        category_name: str
    ) -> DynamicScrapingResult:
        """
        Scrape a category directly without subcategory discovery.
        This method creates a single agent to scrape the provided category URL.

        Args:
            category_url: Direct URL to the category page to scrape
            vendor: Vendor name (e.g., 'asda', 'tesco')
            category_name: Human-readable category name

        Returns:
            DynamicScrapingResult with products and metadata
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"Starting direct scraping for {vendor}/{category_name}")
            self.logger.info(f"Session ID: {self.session_id}")
            self.logger.info(f"Max pages per category: {self.max_pages_per_category}")

            # Create multi-agent crew for this category (like main.py)
            product_scraper = CategoryScraperAgent.create_product_scraper_agent(category_name, agent_id=1)
            data_extractor = CategoryScraperAgent.create_data_extractor_agent(category_name, agent_id=2)
            data_validator = CategoryScraperAgent.create_data_validator_agent(category_name, agent_id=3)

            # Create tasks for each agent
            category_data = {"name": category_name, "url": category_url}

            scraping_task = CategoryScraperAgent.create_product_scraping_task(
                category_data=category_data,
                vendor=vendor,
                agent=product_scraper,
                max_pages=self.max_pages_per_category
            )

            extraction_task = CategoryScraperAgent.create_data_extraction_task(
                category_data=category_data,
                vendor=vendor,
                agent=data_extractor
            )

            validation_task = CategoryScraperAgent.create_data_validation_task(
                category_data=category_data,
                vendor=vendor,
                agent=data_validator
            )

            # Execute with multi-agent CrewAI crew
            crew = Crew(
                agents=[product_scraper, data_extractor, data_validator],
                tasks=[scraping_task, extraction_task, validation_task],
                process=Process.sequential,
                verbose=True
            )

            result = crew.kickoff()

            # Parse results
            products = self._parse_agent_result(result)

            # Create agent result summary
            agent_results = [{
                'agent_id': 1,
                'subcategory': category_name,
                'success': True,
                'products_found': len(products),
                'url': category_url
            }]

            return DynamicScrapingResult(
                success=True,
                products=products,
                total_products=len(products),
                agent_results=agent_results,
                timestamp=datetime.now(),
                session_id=self.session_id,
                vendor=vendor,
                category=category_name
            )

        except Exception as e:
            self.logger.error(f"Direct scraping failed for {vendor}/{category_name}: {str(e)}")
            return DynamicScrapingResult(
                success=False,
                products=[],
                total_products=0,
                agent_results=[{
                    'agent_id': 1,
                    'subcategory': category_name,
                    'success': False,
                    'products_found': 0,
                    'url': category_url,
                    'error': str(e)
                }],
                timestamp=datetime.now(),
                session_id=self.session_id,
                vendor=vendor,
                category=category_name,
                error=f"Direct scraping failed: {str(e)}"
            )


    
    def _scrape_subcategories(self, subcategories: List[Dict], vendor: str) -> DynamicScrapingResult:
        """Scrape products from all subcategories using dynamic agents."""
        try:
            # Create agents and tasks for each subcategory
            agents = []
            tasks = []
            
            for i, subcategory in enumerate(subcategories[:self.max_concurrent_agents]):
                agent = CategoryScraperAgent.create_agent(subcategory['name'], i+1)
                task = CategoryScraperAgent.create_scraping_task(
                    subcategory, vendor, agent, max_pages=self.max_pages_per_category
                )

                agents.append(agent)
                tasks.append(task)
            
            # Create crew for concurrent execution
            scraping_crew = Crew(
                agents=agents,
                tasks=tasks,
                process=Process.hierarchical,  # or Process.sequential based on preference
                verbose=True
            )
            
            # Execute scraping
            results = scraping_crew.kickoff()
            
            # Process and combine results
            all_products = []
            agent_results = []
            
            # Parse results from each agent
            # This would need to be adapted based on actual CrewAI output format
            for i, result in enumerate(results if isinstance(results, list) else [results]):
                parsed_products = self._parse_agent_result(result)
                all_products.extend(parsed_products)
                agent_results.append({
                    'agent_id': i+1,
                    'subcategory': subcategories[i]['name'],
                    'products_found': len(parsed_products),
                    'success': len(parsed_products) > 0
                })
            
            return DynamicScrapingResult(
                success=True,
                products=all_products,
                agent_results=agent_results
            )
            
        except Exception as e:
            self.logger.error(f"Subcategory scraping failed: {e}")
            return DynamicScrapingResult(
                success=False,
                error=f"Subcategory scraping failed: {str(e)}"
            )
    
    def _parse_agent_result(self, result: Any) -> List[StandardizedProduct]:
        """Parse agent result into StandardizedProduct list."""
        # This would need to be implemented based on actual CrewAI output format
        # For now, return a placeholder
        return []
