#!/usr/bin/env python3
"""Dynamic multi-agent scraper using CrewAI's native orchestration."""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool

from ..tools.stagehand_tool import EcommerceStagehandTool
from ..tools.product_data_validator import ProductDataValidator
from ..tools.price_extractor import PriceExtractor
from ..tools.image_extractor import ImageExtractor
from ..config.settings import settings
from ..models.product import StandardizedProduct

logger = logging.getLogger(__name__)


class DynamicScrapingResult:
    """Result from dynamic scraping operation."""
    
    def __init__(self, success: bool, products: List[StandardizedProduct] = None, 
                 error: str = None, agent_results: List[Dict] = None):
        self.success = success
        self.products = products or []
        self.error = error
        self.agent_results = agent_results or []
        self.timestamp = datetime.now()
        self.total_products = len(self.products)


class CategoryDiscoveryAgent:
    """Agent responsible for discovering subcategories from a main category page."""
    
    @staticmethod
    def create_agent() -> Agent:
        """Create a category discovery agent."""
        return Agent(
            role="Category Discovery Specialist",
            goal="Discover and extract all subcategory links from category pages",
            backstory="""You are an expert at analyzing ecommerce category pages and 
            identifying all available subcategories and product listing pages. You can 
            navigate complex site structures and extract relevant URLs for further scraping.""",
            tools=[EcommerceStagehandTool.create_with_context()],
            verbose=True,
            allow_delegation=False
        )
    
    @staticmethod
    def create_discovery_task(category_url: str, vendor: str) -> Task:
        """Create a task for discovering subcategories."""
        return Task(
            description=f"""
            Navigate to the category page: {category_url}
            
            Your task is to:
            1. Navigate to the category page and analyze its structure
            2. Identify all subcategory links and product listing pages
            3. Extract the URLs, names, and any relevant metadata
            4. Return a structured list of subcategories with their URLs
            
            Focus on finding:
            - Direct subcategory links
            - Product listing pages
            - Pagination links if applicable
            
            Vendor: {vendor}
            Category URL: {category_url}
            """,
            expected_output="""A JSON list of subcategories with structure:
            [
                {
                    "name": "subcategory_name",
                    "url": "full_url_to_subcategory", 
                    "type": "subcategory|product_list",
                    "product_count_estimate": number_or_null
                }
            ]""",
            agent=CategoryDiscoveryAgent.create_agent()
        )


class SubCategoryScraperAgent:
    """Agent responsible for scraping products from a specific subcategory."""
    
    @staticmethod
    def create_agent(subcategory_name: str, agent_id: int) -> Agent:
        """Create a subcategory scraper agent."""
        return Agent(
            role=f"SubCategory Product Scraper #{agent_id}",
            goal=f"Extract all product data from the {subcategory_name} subcategory",
            backstory=f"""You are a specialized product scraper focused on the 
            {subcategory_name} category. You excel at navigating product listings, 
            extracting detailed product information, and handling pagination to 
            ensure complete data collection.""",
            tools=[
                EcommerceStagehandTool.create_with_context(),
                ProductDataValidator(),
                PriceExtractor(),
                ImageExtractor()
            ],
            verbose=True,
            allow_delegation=False
        )
    
    @staticmethod
    def create_scraping_task(subcategory_data: Dict, vendor: str, agent: Agent, max_pages: Optional[int] = None) -> Task:
        """Create a task for scraping a specific subcategory."""
        pagination_instruction = f"Handle pagination to get products from up to {max_pages} pages" if max_pages else "Handle pagination to get all products"

        return Task(
            description=f"""
            Scrape products from the subcategory: {subcategory_data['name']}
            URL: {subcategory_data['url']}
            Max pages to scrape: {max_pages or 'All available'}

            Your task is to:
            1. Navigate to the subcategory page
            2. Extract all visible products with complete details
            3. {pagination_instruction}
            4. Validate and standardize the product data
            5. Extract high-quality product images
            6. Ensure price information is accurate
            
            For each product, extract:
            - Name/title
            - Price (current and original if on sale)
            - Description
            - Images (main and additional)
            - Availability status
            - Product variants (size, color, etc.)
            - SKU/product ID if available
            - Ratings/reviews if present
            
            Vendor: {vendor}
            Subcategory: {subcategory_data['name']}
            """,
            expected_output="""A JSON list of standardized products:
            [
                {
                    "name": "product_name",
                    "price": "current_price",
                    "original_price": "original_price_if_different",
                    "description": "product_description",
                    "images": ["image_url_1", "image_url_2"],
                    "availability": "in_stock|out_of_stock|limited",
                    "variants": [{"type": "size", "value": "M"}],
                    "sku": "product_sku",
                    "rating": "4.5",
                    "review_count": "123"
                }
            ]""",
            agent=agent
        )


class DynamicMultiAgentScraper:
    """Main orchestrator for dynamic multi-agent scraping."""

    def __init__(self, max_concurrent_agents: int = 3, max_pages_per_category: Optional[int] = None, session_id: str = None):
        """Initialize the dynamic scraper.

        Args:
            max_concurrent_agents: Maximum number of agents to run concurrently
            max_pages_per_category: Maximum pages to scrape per category (None = all pages)
            session_id: Session ID for tracking and logging
        """
        self.max_concurrent_agents = max_concurrent_agents
        self.max_pages_per_category = max_pages_per_category
        self.session_id = session_id or f"dynamic_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger = logging.getLogger(__name__)
    
    def scrape_category_with_subcategories(
        self, 
        category_url: str, 
        vendor: str, 
        category_name: str
    ) -> DynamicScrapingResult:
        """
        Scrape a category by first discovering subcategories, then delegating 
        to specialized agents.
        
        Args:
            category_url: Main category URL to scrape
            vendor: Vendor name (e.g., 'asda', 'tesco')
            category_name: Human-readable category name
            
        Returns:
            DynamicScrapingResult with all products and agent results
        """
        try:
            self.logger.info(f"Starting dynamic scraping for {vendor}/{category_name}")
            
            # Phase 1: Discover subcategories
            discovery_result = self._discover_subcategories(category_url, vendor)
            
            if not discovery_result.success:
                return DynamicScrapingResult(
                    success=False,
                    error=f"Failed to discover subcategories: {discovery_result.error}"
                )
            
            subcategories = discovery_result.subcategories
            self.logger.info(f"Discovered {len(subcategories)} subcategories")
            
            # Phase 2: Create agents for each subcategory
            scraping_result = self._scrape_subcategories(subcategories, vendor)
            
            return scraping_result
            
        except Exception as e:
            self.logger.error(f"Dynamic scraping failed: {e}")
            return DynamicScrapingResult(
                success=False,
                error=f"Dynamic scraping failed: {str(e)}"
            )
    
    def _discover_subcategories(self, category_url: str, vendor: str) -> Any:
        """Discover subcategories using CategoryDiscoveryAgent."""
        try:
            # Create discovery crew
            discovery_agent = CategoryDiscoveryAgent.create_agent()
            discovery_task = CategoryDiscoveryAgent.create_discovery_task(category_url, vendor)
            
            discovery_crew = Crew(
                agents=[discovery_agent],
                tasks=[discovery_task],
                process=Process.sequential,
                verbose=True
            )
            
            # Execute discovery
            result = discovery_crew.kickoff()
            
            # Parse result (assuming it returns JSON-like structure)
            # This would need to be adapted based on actual CrewAI output format
            subcategories = self._parse_discovery_result(result)
            
            return type('DiscoveryResult', (), {
                'success': True,
                'subcategories': subcategories
            })()
            
        except Exception as e:
            self.logger.error(f"Subcategory discovery failed: {e}")
            return type('DiscoveryResult', (), {
                'success': False,
                'error': str(e)
            })()
    
    def _scrape_subcategories(self, subcategories: List[Dict], vendor: str) -> DynamicScrapingResult:
        """Scrape products from all subcategories using dynamic agents."""
        try:
            # Create agents and tasks for each subcategory
            agents = []
            tasks = []
            
            for i, subcategory in enumerate(subcategories[:self.max_concurrent_agents]):
                agent = SubCategoryScraperAgent.create_agent(subcategory['name'], i+1)
                task = SubCategoryScraperAgent.create_scraping_task(
                    subcategory, vendor, agent, max_pages=self.max_pages_per_category
                )

                agents.append(agent)
                tasks.append(task)
            
            # Create crew for concurrent execution
            scraping_crew = Crew(
                agents=agents,
                tasks=tasks,
                process=Process.hierarchical,  # or Process.sequential based on preference
                verbose=True
            )
            
            # Execute scraping
            results = scraping_crew.kickoff()
            
            # Process and combine results
            all_products = []
            agent_results = []
            
            # Parse results from each agent
            # This would need to be adapted based on actual CrewAI output format
            for i, result in enumerate(results if isinstance(results, list) else [results]):
                parsed_products = self._parse_agent_result(result)
                all_products.extend(parsed_products)
                agent_results.append({
                    'agent_id': i+1,
                    'subcategory': subcategories[i]['name'],
                    'products_found': len(parsed_products),
                    'success': len(parsed_products) > 0
                })
            
            return DynamicScrapingResult(
                success=True,
                products=all_products,
                agent_results=agent_results
            )
            
        except Exception as e:
            self.logger.error(f"Subcategory scraping failed: {e}")
            return DynamicScrapingResult(
                success=False,
                error=f"Subcategory scraping failed: {str(e)}"
            )
    
    def _parse_discovery_result(self, result: Any) -> List[Dict]:
        """Parse the discovery result into subcategory list."""
        # This would need to be implemented based on actual CrewAI output format
        # For now, return a placeholder
        return []
    
    def _parse_agent_result(self, result: Any) -> List[StandardizedProduct]:
        """Parse agent result into StandardizedProduct list."""
        # This would need to be implemented based on actual CrewAI output format
        # For now, return a placeholder
        return []
