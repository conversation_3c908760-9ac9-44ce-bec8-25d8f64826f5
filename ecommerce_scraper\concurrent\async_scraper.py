"""Async concurrent scraper using multiple Browserbase sessions."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timezone

from ecommerce_scraper.main import EcommerceScraper
from ecommerce_scraper.schemas.standardized_product import StandardizedProduct
from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

logger = logging.getLogger(__name__)


@dataclass
class ScrapeTask:
    """Individual scraping task."""
    url: str
    vendor: str
    category: str
    task_id: str


@dataclass
class ScrapeResult:
    """Result of a scraping task."""
    task_id: str
    url: str
    vendor: str
    category: str
    products: List[StandardizedProduct]
    success: bool
    error: Optional[str] = None
    duration: Optional[float] = None


class AsyncConcurrentScraper:
    """Async concurrent scraper using multiple Browserbase sessions."""
    
    def __init__(self, max_concurrent_sessions: int = 3, max_concurrent_per_vendor: int = 2):
        """Initialize async scraper.
        
        Args:
            max_concurrent_sessions: Maximum total concurrent sessions
            max_concurrent_per_vendor: Maximum concurrent sessions per vendor
        """
        self.max_concurrent_sessions = max_concurrent_sessions
        self.max_concurrent_per_vendor = max_concurrent_per_vendor
        self.vendor_semaphores: Dict[str, asyncio.Semaphore] = {}
        self.global_semaphore = asyncio.Semaphore(max_concurrent_sessions)
        
    async def scrape_urls_concurrent(self, tasks: List[ScrapeTask]) -> List[ScrapeResult]:
        """Scrape multiple URLs concurrently using separate Browserbase sessions.
        
        Args:
            tasks: List of scraping tasks
            
        Returns:
            List of scrape results
        """
        logger.info(f"Starting concurrent scraping of {len(tasks)} URLs")
        
        # Create semaphores for vendors
        for task in tasks:
            if task.vendor not in self.vendor_semaphores:
                self.vendor_semaphores[task.vendor] = asyncio.Semaphore(self.max_concurrent_per_vendor)
        
        # Create coroutines for all tasks
        coroutines = [self._scrape_single_url(task) for task in tasks]
        
        # Run all tasks concurrently
        results = await asyncio.gather(*coroutines, return_exceptions=True)
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task {tasks[i].task_id} failed with exception: {result}")
                processed_results.append(ScrapeResult(
                    task_id=tasks[i].task_id,
                    url=tasks[i].url,
                    vendor=tasks[i].vendor,
                    category=tasks[i].category,
                    products=[],
                    success=False,
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        logger.info(f"Completed concurrent scraping. Success: {sum(1 for r in processed_results if r.success)}/{len(processed_results)}")
        return processed_results
    
    async def _scrape_single_url(self, task: ScrapeTask) -> ScrapeResult:
        """Scrape a single URL with its own Browserbase session.
        
        Args:
            task: Scraping task
            
        Returns:
            Scrape result
        """
        start_time = datetime.now(timezone.utc)
        
        # Acquire semaphores for rate limiting
        async with self.global_semaphore:
            async with self.vendor_semaphores[task.vendor]:
                logger.info(f"Starting scrape task {task.task_id}: {task.url}")
                
                try:
                    # Create a new EcommerceScraper instance for this task
                    # Each instance will have its own Stagehand session
                    scraper = EcommerceScraper(verbose=False)
                    
                    # Scrape the URL - this will create its own Browserbase session
                    result = await asyncio.to_thread(scraper.scrape_product, task.url)
                    
                    # Extract products from result
                    if result and hasattr(result, 'products') and result.products:
                        products = result.products
                        success = True
                        error = None
                        logger.info(f"Task {task.task_id} completed successfully: {len(products)} products")
                    else:
                        products = []
                        success = False
                        error = "No products found"
                        logger.warning(f"Task {task.task_id} completed but no products found")
                    
                    duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                    
                    return ScrapeResult(
                        task_id=task.task_id,
                        url=task.url,
                        vendor=task.vendor,
                        category=task.category,
                        products=products,
                        success=success,
                        error=error,
                        duration=duration
                    )
                    
                except Exception as e:
                    duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                    logger.error(f"Task {task.task_id} failed: {e}")
                    
                    return ScrapeResult(
                        task_id=task.task_id,
                        url=task.url,
                        vendor=task.vendor,
                        category=task.category,
                        products=[],
                        success=False,
                        error=str(e),
                        duration=duration
                    )


async def scrape_multiple_urls(urls_data: List[Dict[str, str]], 
                              max_concurrent: int = 3,
                              max_per_vendor: int = 2) -> List[ScrapeResult]:
    """Convenience function to scrape multiple URLs concurrently.
    
    Args:
        urls_data: List of dicts with 'url', 'vendor', 'category' keys
        max_concurrent: Maximum concurrent sessions
        max_per_vendor: Maximum concurrent sessions per vendor
        
    Returns:
        List of scrape results
    """
    # Create tasks
    tasks = [
        ScrapeTask(
            url=data['url'],
            vendor=data['vendor'],
            category=data['category'],
            task_id=f"{data['vendor']}_{hash(data['url'])}"
        )
        for data in urls_data
    ]
    
    # Create scraper and run
    scraper = AsyncConcurrentScraper(max_concurrent, max_per_vendor)
    return await scraper.scrape_urls_concurrent(tasks)


# Example usage
async def main():
    """Example usage of async concurrent scraper."""
    urls_to_scrape = [
        {"url": "https://demo.vercel.store/products/acme-mug", "vendor": "demo", "category": "mugs"},
        {"url": "https://demo.vercel.store/products/acme-circles-t-shirt", "vendor": "demo", "category": "shirts"},
        {"url": "https://demo.vercel.store/products/acme-drawstring-bag", "vendor": "demo", "category": "bags"},
    ]
    
    results = await scrape_multiple_urls(urls_to_scrape, max_concurrent=2, max_per_vendor=1)
    
    for result in results:
        print(f"Task {result.task_id}: {'✅' if result.success else '❌'} "
              f"({len(result.products)} products, {result.duration:.1f}s)")


if __name__ == "__main__":
    asyncio.run(main())
